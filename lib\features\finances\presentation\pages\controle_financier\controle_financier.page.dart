import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:Kairos/core/widgets/layout/custom_app_bar.widget.dart';
import 'package:Kairos/core/widgets/common/hero_widget.dart';
import 'package:Kairos/core/enums/header_enums.dart';
import 'package:Kairos/features/schools/domain/entities/etablissement_utilisateur.dart';
import 'package:Kairos/features/student_records/domain/entities/enfant_tuteur_entity.dart';
import 'package:Kairos/features/finances/presentation/bloc/finances_cubit.dart';
import 'package:Kairos/features/finances/presentation/bloc/finances_state.dart';

import 'package:Kairos/core/widgets/indicators/custom_spinner.dart';

import 'package:Kairos/features/authentication/data/datasources/auth_local_datasource.dart';
import 'package:Kairos/core/di/injection_container.dart';

class ControleFinancierPage extends StatefulWidget {
  final EtablissementUtilisateur school;
  final EnfantTuteurEntity? etudiant;
  final String? scannedValue;

  const ControleFinancierPage({
    super.key,
    required this.school,
    this.etudiant,
    this.scannedValue,
  });

  @override
  State<ControleFinancierPage> createState() => _ControleFinancierPageState();
}

class _ControleFinancierPageState extends State<ControleFinancierPage> {
  @override
  void initState() {
    super.initState();
    _initializeFinancialCheck();
  }

  void _initializeFinancialCheck() async {
    // Load finances data with financial status check
    final financesCubit = context.read<FinancesCubit>();
    final authLocalDataSource = sl<AuthLocalDataSource>();

    // Get student code - use etudiant if available, otherwise use scanned value
    final codeEtudiant = widget.etudiant?.codeEtudiant ?? widget.scannedValue ?? '';

    if (codeEtudiant.isNotEmpty) {
      // Get phone number from SharedPreferences
      final phoneNumber = await authLocalDataSource.getPhoneNumber();

      if (phoneNumber != null) {
        // Load finances data with financial status
        financesCubit.loadFinancesDataWithStatus(
          codeEtab: widget.school.codeEtab,
          telephone: phoneNumber,
          codeEtudiant: codeEtudiant,
          codeUtilisateur: widget.school.codeUtilisateur,
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: CustomScrollView(
        slivers: [
          // Transparent AppBar with back button
          CustomAppBar(
            pageSection: HeaderEnum.controleFinancier,
            title: "CONTRÔLE FINANCIER",
            enfantDuTuteur: widget.etudiant,
          ),
          
          // Hero Widget Section
          SliverToBoxAdapter(
            child: HeroWidget(
              pageSection: HeaderEnum.controleFinancier,
              enfantDuTuteur: widget.etudiant,
              etablissementUser: widget.school,
            ),
          ),

          // Main Content
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 7.0),
              child: Column(
                children: [
                  const SizedBox(height: 15),
                  
                  // Financial Status Section
                  BlocBuilder<FinancesCubit, FinancesState>(
                    builder: (context, financesState) {
                      if (financesState is FinancesLoading) {
                        return const Center(
                          child: CustomSpinner(),
                        );
                      } else if (financesState is FinancesError) {
                        return Center(
                          child: Column(
                            children: [
                              const Icon(
                                Icons.error_outline,
                                color: Colors.red,
                                size: 48,
                              ),
                              const SizedBox(height: 16),
                              Text(
                                'Erreur lors de la vérification du statut financier',
                                style: TextStyle(
                                  color: Colors.red,
                                  fontSize: 16,
                                ),
                                textAlign: TextAlign.center,
                              ),
                              const SizedBox(height: 8),
                              Text(
                                financesState.message,
                                style: const TextStyle(
                                  color: Colors.grey,
                                  fontSize: 14,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        );
                      } else if (financesState is FinancesLoaded &&
                                 financesState.financialStatus != null) {
                        return _buildStatusSection(financesState.financialStatus!.enRegle);
                      }

                      return const SizedBox.shrink();
                    },
                  ),

                  const SizedBox(height: 15),

                  // Unpaid Fees Section (only show if not "en règle")
                  BlocBuilder<FinancesCubit, FinancesState>(
                    builder: (context, financesState) {
                      if (financesState is FinancesLoaded &&
                          financesState.financialStatus != null &&
                          !financesState.financialStatus!.enRegle) {
                        return _buildUnpaidFeesSection();
                      }
                      return const SizedBox.shrink();
                    },
                  ),

                  const SizedBox(height: 20),

                  // New Control Button
                  _buildNewControlButton(),

                  const SizedBox(height: 20),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusSection(bool enRegle) {
    if (enRegle) {
      return _buildSuccessStatus();
    } else {
      return _buildAlertStatus();
    }
  }

  Widget _buildSuccessStatus() {
    // Success UI based on Figma design (node 453-1471)
    return Container(
      width: double.infinity,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Success icon
          Container(
            width: 63,
            height: 63,
            decoration: const BoxDecoration(
              color: Color(0xFF3EB290),
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.check,
              color: Colors.white,
              size: 32,
            ),
          ),
          const SizedBox(width: 15),
          // Status text
          const Text(
            'EN REGLE',
            style: TextStyle(
              fontFamily: 'Roboto',
              fontWeight: FontWeight.w500,
              fontSize: 36,
              color: Color(0xFF3EB290),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAlertStatus() {
    // Alert UI based on Figma design (node 507-1103)
    return Container(
      width: double.infinity,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Alert icon
          Container(
            width: 63,
            height: 63,
            decoration: const BoxDecoration(
              color: Color(0xFFFF3B30),
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.close,
              color: Colors.white,
              size: 32,
            ),
          ),
          const SizedBox(width: 15),
          // Status text
          const Text(
            'PAS EN REGLE',
            style: TextStyle(
              fontFamily: 'Roboto',
              fontWeight: FontWeight.w500,
              fontSize: 36,
              color: Color(0xFFFF3B30),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUnpaidFeesSection() {
    return BlocBuilder<FinancesCubit, FinancesState>(
      builder: (context, financesState) {
        if (financesState is FinancesLoading) {
          return const Center(child: CustomSpinner());
        } else if (financesState is FinancesError) {
          return Center(
            child: Text(
              'Erreur lors du chargement des frais impayés',
              style: TextStyle(color: Colors.red),
            ),
          );
        } else if (financesState is FinancesLoaded) {
          final unpaidFees = financesState.unpaidFeesResponse?.fraisImPayes ?? [];
          
          if (unpaidFees.isEmpty) {
            return const SizedBox.shrink();
          }

          // Calculate total unpaid amount
          final totalUnpaid = unpaidFees.fold<double>(
            0.0,
            (sum, fee) => sum + (fee.montantFrais?.toDouble() ?? 0.0),
          );

          return Column(
            children: [
              // Unpaid fees header with accordion
              Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  border: Border(
                    bottom: BorderSide(color: Colors.black, width: 1),
                  ),
                ),
                child: Container(
                  height: 30,
                  child: Stack(
                    children: [
                      // Orange indicator line
                      Positioned(
                        bottom: 0,
                        left: 0,
                        right: 0,
                        child: Container(
                          height: 7,
                          color: const Color(0xFFE5920C),
                        ),
                      ),
                      // Content
                      Positioned(
                        left: 33,
                        top: 0,
                        right: 33,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'FRAIS IMPAYÉS ${totalUnpaid.toInt()} XOF',
                              style: const TextStyle(
                                fontFamily: 'Inter',
                                fontWeight: FontWeight.w400,
                                fontSize: 16,
                                color: Colors.black,
                              ),
                            ),
                            const Icon(
                              Icons.expand_more,
                              size: 18,
                              color: Colors.black,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          );
        }
        
        return const SizedBox.shrink();
      },
    );
  }

  Widget _buildNewControlButton() {
    return Container(
      width: 335,
      height: 50,
      decoration: BoxDecoration(
        color: const Color(0xFF06B6E4),
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF16196E).withValues(alpha: 0.08),
            offset: const Offset(0, 6),
            blurRadius: 16,
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(10),
          onTap: () {
            // Handle new control action
            _handleNewControl();
          },
          child: const Center(
            child: Text(
              'NOUVEAU CONTRÔLE',
              style: TextStyle(
                fontFamily: 'Inter',
                fontWeight: FontWeight.w600,
                fontSize: 16,
                color: Colors.white,
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _handleNewControl() {
    // TODO: Implement new control functionality
    // This could trigger a new QR scan or other control actions
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Fonctionnalité en cours de développement'),
        backgroundColor: Colors.blue,
      ),
    );
  }
}
