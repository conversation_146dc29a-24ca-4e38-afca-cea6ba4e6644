import 'package:Kairos/core/api/api_client.dart';
import 'package:Kairos/core/api/api_endpoints.dart';
import 'package:Kairos/core/error/exceptions.dart';
import 'package:Kairos/features/educational_resources/data/models/resource_pedagogique_model.dart';
import 'package:Kairos/features/student_records/data/models/dossier_attachment_model.dart';
import 'package:dio/dio.dart';

/// Abstract class defining the contract for educational resources remote data source
abstract class RessourcesPedagogiquesRemoteDataSource {
  /// Fetches educational resources from the remote API
  Future<List<ResourcePedagogiqueModel>> getRessourcesPedagogiques({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  });

  /// Fetches educational resources filtered by year from the remote API
  Future<List<ResourcePedagogiqueModel>> getRessourcesPedagogiquesFiltres({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    required String annee,
    String? codeUtilisateur,
  });

  /// Fetches attachment for a specific educational resource
  Future<DossierAttachmentModel> getRessourcePedagogiqueAttachment({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    required int idRessource,
    String? codeUtilisateur,
  });
}

/// Implementation of RessourcesPedagogiquesRemoteDataSource
class RessourcesPedagogiquesRemoteDataSourceImpl implements RessourcesPedagogiquesRemoteDataSource {
  final ApiClient apiClient;

  RessourcesPedagogiquesRemoteDataSourceImpl({
    required this.apiClient,
  });

  @override
  Future<List<ResourcePedagogiqueModel>> getRessourcesPedagogiques({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  }) async {
    // Return mock data for testing since API is not yet implemented
    await Future.delayed(const Duration(seconds: 1)); // Simulate network delay

    return [ 
      ResourcePedagogiqueModel(
        typeRessource: 'TD à rendre',
        couleurTypeRessource: '#A5351F',
        dateCreation: '2025-04-09',
        titre: 'Adressage_IP.pdf',
        description: 'Ce TD est à rendre au plus tard le jeudi sur cette adresse : <EMAIL>',
        professeur: 'Amadou Ba',
        classe: 'LPTI1',
        hasAttachment: true,
        idRessource: 1,
        contenuDetaille: 'Ce TD est à rendre au plus tard le jeudi sur cette adresse : <EMAIL>',
        emailProfesseur: '<EMAIL>',
      ),
      ResourcePedagogiqueModel(
        typeRessource: 'Support de cours',
        couleurTypeRessource: '#8E0101',
        dateCreation: '2025-03-05',
        titre: 'Nomenclature_IOT.pdf',
        description: 'Support de cours sur la nomenclature IoT',
        professeur: 'Amadou Ba',
        classe: 'LPTI1',
        hasAttachment: true,
        idRessource: 2,
        contenuDetaille: 'Support de cours détaillé sur la nomenclature des objets connectés (IoT)',
      ),
      ResourcePedagogiqueModel(
        typeRessource: 'Support de cours',
        couleurTypeRessource: '#8E0101',
        dateCreation: '2025-04-06',
        titre: 'Architecture_reseaux.pdf',
        description: 'Architecture des réseaux informatiques',
        professeur: 'James Kiou',
        classe: 'LPTI1',
        hasAttachment: true,
        idRessource: 3,
        contenuDetaille: 'Cours complet sur l\'architecture des réseaux informatiques modernes',
      ),
      ResourcePedagogiqueModel(
        typeRessource: 'Support de cours',
        couleurTypeRessource: '#8E0101',
        dateCreation: '2025-04-01',
        titre: 'Routage_statique.pdf',
        description: 'Configuration du routage statique',
        professeur: 'James Kiou',
        classe: 'LPTI1',
        hasAttachment: true,
        idRessource: 4,
        contenuDetaille: 'Guide pratique pour la configuration du routage statique sur les équipements réseau',
      ),
      ResourcePedagogiqueModel(
        typeRessource: 'Support de cours',
        couleurTypeRessource: '#8E0101',
        dateCreation: '2025-03-08',
        titre: 'Cours_Active_Directory_VE.pdf',
        description: 'Active Directory - Version étudiante',
        professeur: 'Doudou Ndiaye',
        classe: 'LPTI1',
        hasAttachment: true,
        idRessource: 5,
        contenuDetaille: 'Cours complet sur Active Directory adapté pour les étudiants',
      ),
      ResourcePedagogiqueModel(
        typeRessource: 'Support de cours',
        couleurTypeRessource: '#8E0101',
        dateCreation: '2025-03-08',
        titre: 'Cours_Active_Directory_VF.pdf',
        description: 'Active Directory - Version finale',
        professeur: 'Doudou Ndiaye',
        classe: 'LPTI1',
        hasAttachment: true,
        idRessource: 6,
        contenuDetaille: 'Version finale du cours sur Active Directory avec tous les détails avancés',
      ),
      ResourcePedagogiqueModel(
        typeRessource: 'Support de cours',
        couleurTypeRessource: '#8E0101',
        dateCreation: '2025-03-06',
        titre: 'Guide_installation_GNS3.pdf',
        description: 'Guide d\'installation GNS3',
        professeur: 'Doudou Ndiaye',
        classe: 'LPTI1',
        hasAttachment: true,
        idRessource: 7,
        contenuDetaille: 'Guide détaillé pour l\'installation et la configuration de GNS3',
      ),
      ResourcePedagogiqueModel(
        typeRessource: 'Support de cours',
        couleurTypeRessource: '#8E0101',
        dateCreation: '2025-03-05',
        titre: 'Guide_VMWARE.pdf',
        description: 'Guide VMware pour virtualisation',
        professeur: 'Doudou Ndiaye',
        classe: 'LPTI1',
        hasAttachment: true,
        idRessource: 8,
        contenuDetaille: 'Guide complet pour l\'utilisation de VMware dans un environnement de formation',
      ),
    ];
  }

  @override
  Future<List<ResourcePedagogiqueModel>> getRessourcesPedagogiquesFiltres({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    required String annee,
    String? codeUtilisateur,
  }) async {
    // Return filtered mock data for testing
    await Future.delayed(const Duration(seconds: 1)); // Simulate network delay

    // Filter mock data by year (for demo purposes, return subset based on year)
    if (annee == '2024') {
      return [
        ResourcePedagogiqueModel(
          typeRessource: 'Support de cours',
          couleurTypeRessource: '#8E0101',
          dateCreation: '2024-12-15',
          titre: 'Cours_ancien_2024.pdf',
          description: 'Ancien cours de 2024',
          professeur: 'Professeur Test',
          classe: 'LPTI1',
          hasAttachment: true,
          idRessource: 100,
          contenuDetaille: 'Contenu du cours de l\'année 2024',
        ),
      ];
    }

    // Return current year data (same as getRessourcesPedagogiques for demo)
    return getRessourcesPedagogiques(
      codeEtab: codeEtab,
      telephone: telephone,
      codeEtudiant: codeEtudiant,
      codeUtilisateur: codeUtilisateur,
    );
  }

  @override
  Future<DossierAttachmentModel> getRessourcePedagogiqueAttachment({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    required int idRessource,
    String? codeUtilisateur,
  }) async {
    try {
      Map<String, dynamic> queryParams = {
        'codeEtab': codeEtab,
        'telephone': telephone.replaceAll("+", ""),
        'codeEtudiant': codeEtudiant,
        'idRessource': idRessource.toString(),
      };

      if (codeUtilisateur != null && codeUtilisateur.isNotEmpty) {
        queryParams['codeUtilisateur'] = codeUtilisateur;
      }

      final response = await apiClient.getWithToken(
        ApiEndpoints.memosEtudiants, // Reusing the same endpoint pattern as dossiers
        queryParameters: queryParams,
      );

      return DossierAttachmentModel.fromJson(response.data);
    } on DioException catch (e) {
      throw ServerException(e.message ?? 'Server error occurred');
    } catch (e) {
      throw ServerException('Unexpected error occurred');
    }
  }
}
