import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:dartz/dartz.dart'; // Import Either for handling results
import '../../../../core/error/failures.dart'; // Import Failure for error handling
import '../../domain/entities/finances_response_entity.dart'; // Import FinancesResponseEntity
import '../../domain/usecases/get_paid_fees_usecase.dart';
import '../../domain/usecases/get_unpaid_fees_usecase.dart';
import '../../domain/usecases/get_filtered_paid_fees_usecase.dart';
import 'finances_state.dart';
import '../../domain/entities/finances_unpaid_response_entity.dart';

/// Finances Cubit for managing finances state
class FinancesCubit extends Cubit<FinancesState> {
  final GetPaidFeesUseCase getPaidFeesUseCase;
  final GetUnpaidFeesUseCase getUnpaidFeesUseCase;
  final GetFilteredPaidFeesUseCase getFilteredPaidFeesUseCase;

  FinancesCubit({
    required this.getPaidFeesUseCase,
    required this.getUnpaidFeesUseCase,
    required this.getFilteredPaidFeesUseCase,
  }) : super(const FinancesInitial());

  /// Load finances data for a student
  Future<void> loadFinancesData({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  }) async {
    emit(const FinancesLoading());

    try {
      // Load both paid and unpaid fees concurrently
      final results = await Future.wait([
        getPaidFeesUseCase(GetPaidFeesParams(
          codeEtab: codeEtab,
          telephone: telephone,
          codeEtudiant: codeEtudiant,
          codeUtilisateur: codeUtilisateur,
        )),
        getUnpaidFeesUseCase(GetUnpaidFeesParams(
          codeEtab: codeEtab,
          telephone: telephone,
          codeEtudiant: codeEtudiant,
          codeUtilisateur: codeUtilisateur,
        )),
      ]);

      // Cast results to their expected types
      final paidFeesResult = results[0] as Either<Failure, FinancesResponseEntity>;
      final unpaidFeesResult = results[1] as Either<Failure, FinancesUnpaidResponseEntity>;

      // Handle results
      // Check if either call failed
      if (paidFeesResult.isLeft() || unpaidFeesResult.isLeft()) {
        // Get the first error from either result
        final error = paidFeesResult.isLeft()
            ? paidFeesResult.fold((l) => l, (r) => null) // Extract failure from paidFeesResult if it's Left
            : unpaidFeesResult.fold((l) => l, (r) => null); // Extract failure from unpaidFeesResult if paidFeesResult is Right

        // Emit error state with the failure message
        emit(FinancesError(error?.toString() ?? 'Unknown error occurred'));
      } else {
        // Both calls were successful (Right)
        final paidFeesResponse = paidFeesResult.fold(
          (l) => null, // If Left, return null for the response (should not happen based on the check above, but handle defensively)
          (r) => r as FinancesResponseEntity?, // If Right, extract and cast the FinancesResponseEntity
        );
        final unpaidFeesResponse = unpaidFeesResult.fold(
          (l) => null, // If Left, return null for the response (should not happen based on the check above, but handle defensively)
          (r) => r as FinancesUnpaidResponseEntity?, // If Right, extract and cast the FinancesUnpaidResponseEntity
        );
        emit(FinancesLoaded(
          paidFeesResponse: paidFeesResponse, // Pass the FinancesResponseEntity
          unpaidFeesResponse: unpaidFeesResponse, // Pass the FinancesUnpaidResponseEntity
        ));
      }
    } catch (e) {
      // Catch any unexpected exceptions during the process
      emit(FinancesError(e.toString()));
    }
  }

  /// Load finances data with date filter for paid fees
  Future<void> loadFinancesDataWithDateFilter({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    required String dateDebut,
    required String dateFin,
    String? codeUtilisateur,
  }) async {
    emit(const FinancesLoading());

    try {
      // Load filtered paid fees and unpaid fees concurrently
      final results = await Future.wait([
        getFilteredPaidFeesUseCase(GetFilteredPaidFeesParams(
          codeEtab: codeEtab,
          telephone: telephone,
          codeEtudiant: codeEtudiant,
          dateDebut: dateDebut,
          dateFin: dateFin,
          codeUtilisateur: codeUtilisateur,
        )),
        getUnpaidFeesUseCase(GetUnpaidFeesParams(
          codeEtab: codeEtab,
          telephone: telephone,
          codeEtudiant: codeEtudiant,
          codeUtilisateur: codeUtilisateur,
        )),
      ]);

      // Cast results to their expected types
      final paidFeesResult = results[0] as Either<Failure, FinancesResponseEntity>;
      final unpaidFeesResult = results[1] as Either<Failure, FinancesUnpaidResponseEntity>;

      // Handle results
      if (paidFeesResult.isLeft() || unpaidFeesResult.isLeft()) {
        // Get the first error from either result
        final error = paidFeesResult.isLeft()
            ? paidFeesResult.fold((l) => l, (r) => null)
            : unpaidFeesResult.fold((l) => l, (r) => null);

        // Emit error state with the failure message
        emit(FinancesError(error?.toString() ?? 'Unknown error occurred'));
      } else {
        // Both calls were successful
        final paidFeesResponse = paidFeesResult.fold(
          (l) => null,
          (r) => r as FinancesResponseEntity?,
        );
        final unpaidFeesResponse = unpaidFeesResult.fold(
          (l) => null,
          (r) => r as FinancesUnpaidResponseEntity?,
        );
        emit(FinancesLoaded(
          paidFeesResponse: paidFeesResponse,
          unpaidFeesResponse: unpaidFeesResponse,
        ));
      }
    } catch (e) {
      // Catch any unexpected exceptions during the process
      emit(FinancesError(e.toString()));
    }
  }

  /// Refresh finances data
  Future<void> refresh({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    required String codeUtilisateur,
  }) async {
    await loadFinancesData(
      codeEtab: codeEtab,
      telephone: telephone,
      codeEtudiant: codeEtudiant,
      codeUtilisateur: codeUtilisateur,
    );
  }
}
