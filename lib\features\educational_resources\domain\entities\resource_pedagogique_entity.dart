import 'package:equatable/equatable.dart';

/// Domain entity for an educational resource item
class ResourcePedagogiqueEntity extends Equatable {
  final String typeRessource;
  final String couleurTypeRessource;
  final String dateCreation;
  final String titre;
  final String description;
  final String professeur;
  final String classe;
  final bool hasAttachment;
  final int idRessource;
  final String? contenuDetaille;
  final String? emailProfesseur;

  const ResourcePedagogiqueEntity({
    required this.typeRessource,
    required this.couleurTypeRessource,
    required this.dateCreation,
    required this.titre,
    required this.description,
    required this.professeur,
    required this.classe,
    required this.hasAttachment,
    required this.idRessource,
    this.contenuDetaille,
    this.emailProfesseur,
  });

  @override
  List<Object?> get props => [
        typeRessource,
        couleurTypeRessource,
        dateCreation,
        titre,
        description,
        professeur,
        classe,
        hasAttachment,
        idRessource,
        contenuDetaille,
        emailProfesseur,
      ];
}
