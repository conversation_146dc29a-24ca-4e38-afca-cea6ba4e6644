import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:Kairos/core/error/failures.dart';
import 'package:Kairos/core/usecases/usecase.dart';
import 'package:Kairos/features/educational_resources/domain/entities/resource_pedagogique_entity.dart';
import 'package:Kairos/features/educational_resources/domain/repositories/ressources_pedagogiques_repository.dart';

/// Use case for fetching educational resources filtered by year
class GetRessourcesPedagogiquesFiltresUseCase implements UseCase<List<ResourcePedagogiqueEntity>, GetRessourcesPedagogiquesFiltresParams> {
  final RessourcesPedagogiquesRepository repository;

  GetRessourcesPedagogiquesFiltresUseCase(this.repository);

  @override
  Future<Either<Failure, List<ResourcePedagogiqueEntity>>> call(
      GetRessourcesPedagogiquesFiltresParams params) async {
    return await repository.getRessourcesPedagogiquesFiltres(
      codeEtab: params.codeEtab,
      telephone: params.telephone,
      codeEtudiant: params.codeEtudiant,
      annee: params.annee,
      codeUtilisateur: params.codeUtilisateur,
    );
  }
}

/// Parameters for GetRessourcesPedagogiquesFiltresUseCase
class GetRessourcesPedagogiquesFiltresParams extends Equatable {
  final String codeEtab;
  final String telephone;
  final String codeEtudiant;
  final String annee;
  final String? codeUtilisateur;

  const GetRessourcesPedagogiquesFiltresParams({
    required this.codeEtab,
    required this.telephone,
    required this.codeEtudiant,
    required this.annee,
    this.codeUtilisateur,
  });

  @override
  List<Object?> get props => [codeEtab, telephone, codeEtudiant, annee, codeUtilisateur];
}
