﻿
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/error/failures.dart';

import '../../domain/entities/etablissement_entity.dart';
import '../../domain/usecases/get_schools_usecase.dart';
import '../../domain/usecases/get_user_schools_usecase.dart';

import 'schools_state.dart';
import '../../domain/entities/etablissement_utilisateur.dart';

/// Schools Cubit for managing schools state
class SchoolsCubit extends Cubit<SchoolsState> {
  /// Use case for fetching general schools data.
  final GetSchoolsUseCase getSchoolsUseCase;

  /// Use case for fetching user-specific schools data.
  final GetUserSchoolsUseCase getUserSchoolsUseCase;

  /// Constructor for SchoolsCubit.
  SchoolsCubit(
    this.getSchoolsUseCase,
    this.getUserSchoolsUseCase,
  ) : super(SchoolsInitial());

  /// Fetch general schools data.
  Future<void> getSchools() async {
    emit(const SchoolsLoading()); // Indicate loading state

    // GetSchoolsUseCase takes NoParams and returns List<EtablissementEntity>
    final failureOrSchools = await getSchoolsUseCase(); // Corrected call

    failureOrSchools.fold(
      (failure) {
        // Handle failure and emit error state
        emit(SchoolsError(_mapFailureToMessage(failure)));
      },
      (List<EtablissementEntity> schools) { // Expect List<EtablissementEntity>
        // Emit success state with fetched schools
        // Use GeneralSchoolsLoaded for List<EtablissementEntity>
        emit(GeneralSchoolsLoaded(data: schools)); // Corrected state emission
      },
    );
  }

  /// Fetch user-specific schools data.
  Future<List<EtablissementUtilisateur>> getUserSchools(String phoneNumber) async { // Method should accept phone number
    emit(const SchoolsLoading()); // Indicate loading state
    final List<EtablissementUtilisateur> listuserSchools = [];

    // GetUserSchoolsUseCase takes String and returns List<EtablissementUtilisateur>
    final failureOrUserSchools = await getUserSchoolsUseCase(phoneNumber); // Corrected call

    failureOrUserSchools.fold(
      (failure) {
        // Handle failure and emit error state
        emit(SchoolsError(_mapFailureToMessage(failure)));
        return [];
      },
      (List<EtablissementUtilisateur> userSchools) { // Expect List<EtablissementUtilisateur>
        listuserSchools.addAll(userSchools);
        emit(SchoolsLoaded(data: userSchools)); // Corrected state emission
        return userSchools;
      },
    );
    return listuserSchools;

  }

  /// Map Failure to a user-friendly message
  String _mapFailureToMessage(Failure failure) {
    switch (failure.runtimeType) {
      case ServerFailure:
        return (failure as ServerFailure).message ?? 'Server Error';
      case AuthenticationFailure:
        return (failure as AuthenticationFailure).message ?? 'Authentication Error'; // Corrected message
      case NetworkFailure:
        return (failure as NetworkFailure).message ?? 'Network Error';
      default:
        return 'Unexpected Error';
    }
  }

  /// Refresh schools data
  Future<void> refresh() async {
    // Assuming refresh should fetch general schools data
    await getSchools(); // Corrected method call
  }
}
