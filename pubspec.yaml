
name: <PERSON><PERSON>
description: "version mobile de Kairos"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
# MTM5NDEwYWItYzFiYi00OTVlLW
# IyZjYtYjU5OTNjNjdlZmY2
version: 1.0.0-dev-02+12

environment:
  sdk: ^3.7.2

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  flutter_svg: ^2.1.0
  lottie: ^3.3.1
  flutter_native_splash: ^2.4.6
  url_launcher: ^6.3.1
  intl_phone_field: ^3.2.0
  flutter_localizations:
    sdk: flutter
  http: ^1.4.0
  flutter_bloc: ^9.1.1
  equatable: ^2.0.7
  connectivity_plus: ^6.1.4
  shared_preferences: ^2.5.3
  get_it: ^8.0.3
  dio: ^5.8.0+1
  dartz: ^0.10.1
  device_info_plus: ^11.5.0
  mobile_device_identifier: ^0.0.3
  google_fonts: ^6.2.1
  intl: ^0.20.2
  flutter_html: ^3.0.0
  path_provider: ^2.1.5
  open_file: ^3.5.10
  qr_flutter: ^4.1.0
  ai_barcode_scanner: ^7.0.0
  simple_accordion: ^0.1.5
dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  flutter_launcher_icons: "^0.13.1" # Add this line

  # Testing dependencies
  bloc_test: 10.0.0
  mockito: ^5.4.4
  build_runner: ^2.4.13

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/animations/
    - assets/icons/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package


flutter_native_splash:
  color: "#ffffff"
  image: assets/images/logo_kairos.png
  android: true
  ios: true
  web: true

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/images/logo_kairos.png"