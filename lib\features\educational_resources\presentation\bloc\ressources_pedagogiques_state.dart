import 'package:Kairos/features/educational_resources/domain/entities/resource_pedagogique_entity.dart';
import 'package:equatable/equatable.dart';

abstract class RessourcesPedagogiquesState extends Equatable {
  const RessourcesPedagogiquesState();

  @override
  List<Object> get props => [];
}

class RessourcesPedagogiquesInitial extends RessourcesPedagogiquesState {}

class RessourcesPedagogiquesLoading extends RessourcesPedagogiquesState {}

class RessourcesPedagogiquesError extends RessourcesPedagogiquesState {
  final String message;

  const RessourcesPedagogiquesError(this.message);

  @override
  List<Object> get props => [message];
}

// Define an enum for attachment status to manage its state within RessourcesPedagogiquesLoaded
enum AttachmentStatus {
  initial, // No attachment operation in progress
  loading, // Attachment is being fetched/processed
  loaded,  // Attachment has been successfully loaded
  error,   // An error occurred during attachment operation
}

class RessourcesPedagogiquesLoaded extends RessourcesPedagogiquesState {
  final List<ResourcePedagogiqueEntity> ressources;
  // Add attachment-related properties to RessourcesPedagogiquesLoaded state
  final AttachmentStatus attachmentStatus;
  final String? attachmentErrorMessage;

  const RessourcesPedagogiquesLoaded(
    this.ressources, {
    this.attachmentStatus = AttachmentStatus.initial,
    this.attachmentErrorMessage,
  });

  @override
  List<Object> get props => [ressources, attachmentStatus, attachmentErrorMessage ?? ''];

  // Helper method to create a new RessourcesPedagogiquesLoaded state with updated attachment status
  RessourcesPedagogiquesLoaded copyWith({
    List<ResourcePedagogiqueEntity>? ressources,
    AttachmentStatus? attachmentStatus,
    String? attachmentErrorMessage,
  }) {
    return RessourcesPedagogiquesLoaded(
      ressources ?? this.ressources,
      attachmentStatus: attachmentStatus ?? this.attachmentStatus,
      attachmentErrorMessage: attachmentErrorMessage ?? this.attachmentErrorMessage,
    );
  }
}
