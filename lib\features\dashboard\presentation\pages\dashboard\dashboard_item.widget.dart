import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:Kairos/features/absences/presentation/pages/absences_retards/absences_retards.page.dart';
import 'package:Kairos/features/absences/presentation/bloc/absences_retards_cubit.dart';
import 'package:Kairos/features/course_log/presentation/pages/cahier_texte/cahier_texte.page.dart';
import 'package:Kairos/features/student_records/presentation/pages/dossiers_etudiant/dossiers.page.dart';
import 'package:Kairos/features/schedule/presentation/pages/emploi_du_temps/emploi_du_temps.page.dart';
import 'package:Kairos/features/schedule/presentation/bloc/schedule_cubit.dart';
import 'package:Kairos/features/finances/presentation/pages/finances/finances.page.dart';
import 'package:Kairos/features/profile/presentation/pages/carte_virtuelle/carte_virtuelle.page.dart';
import 'package:Kairos/features/grades/presentation/pages/notes/notes.page.dart';
import 'package:Kairos/core/widgets/common/snackbar_widget.dart';
import 'package:Kairos/core/constants/dashboard_strings.dart';
import 'package:Kairos/features/dashboard/data/dashboard_item_type.enum.dart';
import 'package:Kairos/features/schools/domain/entities/etablissement_utilisateur.dart';
import 'package:Kairos/features/student_records/domain/entities/enfant_tuteur_entity.dart';
import 'package:Kairos/features/grades/presentation/bloc/notes_cubit.dart';
import 'package:Kairos/features/finances/presentation/bloc/finances_cubit.dart';
import 'package:Kairos/core/di/injection_container.dart';
import 'package:Kairos/features/course_log/presentation/bloc/course_log_cubit.dart';
import 'package:Kairos/features/student_records/presentation/bloc/dossiers_cubit.dart';
import 'package:Kairos/features/educational_resources/presentation/bloc/ressources_pedagogiques_cubit.dart';
import 'package:Kairos/features/educational_resources/presentation/pages/ressources_pedagogiques.page.dart';
import 'package:Kairos/features/finances/presentation/pages/controle_financier/controle_financier.page.dart';
import 'package:Kairos/features/schools/presentation/bloc/financial_status_cubit.dart';
import 'package:mobile_scanner/mobile_scanner.dart';

class DashboardItem extends StatelessWidget{
  final dynamic title;
  final dynamic subtitle;
  final dynamic iconName;
  final DashboardItemType? itemType;
  final EtablissementUtilisateur school;
  final EnfantTuteurEntity? etudiant;

  const DashboardItem({
    super.key,
    required this.title,
    this.subtitle,
    required this.iconName,
    this.itemType,
    required this.school,
    this.etudiant,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      shape: RoundedRectangleBorder(
        side: const BorderSide(color: Colors.grey, width: 1),
      ),
      color: Theme.of(context).scaffoldBackgroundColor,
      shadowColor: Colors.black,
      child: ListTile(
        dense: true,
        contentPadding:  null,
        // contentPadding: EdgeInsets.only(left: 10),
        title: ListTile(
          contentPadding: null,
            dense: true,
            title: title is Widget ? DefaultTextStyle.merge(style: const TextStyle(fontSize: 11), child: title) : Text(title, style: const TextStyle(fontSize: 11)),
            subtitle: subtitle != null ? Text(subtitle, style: const TextStyle(fontSize: 10)) : null,
          ),
        leading: SvgPicture.asset("assets/icons/$iconName", width: 38, height: 38,),
        trailing: Icon(Icons.arrow_forward_ios, size: 10, weight: 900,),
        onTap: () {
          if (itemType != null) {
            switch(itemType!) {
              case DashboardItemType.notes:
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => BlocProvider(
                      create: (context) => sl<NotesCubit>(),
                      child: NotesPage(school: school, etudiant: etudiant),
                    ),
                    settings: RouteSettings(
                      arguments: {
                        'school': school,
                        'etudiant': etudiant,
                      },
                    ),
                  ),
                );
                break;
              case DashboardItemType.finances:
              
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => BlocProvider(
                        create: (context) => sl<FinancesCubit>(),
                        child: FinancesPage(school: school, etudiant: etudiant),
                      ),
                    ),
                  );
                break;
              case DashboardItemType.absences:
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => BlocProvider(
                      create: (context) => sl<AbsencesRetardsCubit>(),
                      child: AbsencesRetardsPage(school: school, etudiant: etudiant),
                    ),
                    settings: RouteSettings(
                      arguments: {
                        'school': school,
                        'etudiant': etudiant,
                      },
                    ),
                  ),
                );
                break;
              case DashboardItemType.dossiers:
                debugPrint('NAVIGATING TO DOSSIERS PAGE ----> ');
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => BlocProvider(
                      create: (context) => sl<DossiersCubit>(), 
                      child: DossiersPage(school: school, etudiant: etudiant),
                    ),
                    settings: RouteSettings(
                      arguments: {
                        'school': school,
                        'etudiant': etudiant,
                      },
                    ),
                  ),
                );
                break;
              case DashboardItemType.cahierTexte:
                 Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => BlocProvider(
                      create: (context) => sl<CourseLogCubit>(),
                      child: CahierTextePage(school: school, etudiant: etudiant),
                    ),
                    settings: RouteSettings(
                      arguments: {
                        'school': school,
                        'etudiant': etudiant,
                      },
                    ),
                  ),
                );
                break;
              case DashboardItemType.planning:
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => BlocProvider(
                      create: (context) => sl<EmploiDuTempsCubit>(),
                      child: EmploiDuTempsPage(school: school, etudiant: etudiant),
                    ),
                    settings: RouteSettings(
                      arguments: {
                        'school': school,
                        'etudiant': etudiant,
                      },
                    ),
                  ),
                );
                break;
              case DashboardItemType.carteVirtuelle:
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => CarteVirtuellePage(school: school, etudiant: etudiant),
                    settings: RouteSettings(
                      arguments: {
                        'school': school,
                        'etudiant': etudiant,
                      },
                    ),
                  ),
                );
                break;
              case DashboardItemType.controleFinancier:
                // _handleFinancialControl(context, school, etudiant);
                break;
              case DashboardItemType.ressources:
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => BlocProvider(
                      create: (context) => sl<RessourcesPedagogiquesCubit>(),
                      child: RessourcesPedagogiquesPage(school: school, etudiant: etudiant),
                    ),
                    settings: RouteSettings(
                      arguments: {
                        'school': school,
                        'etudiant': etudiant,
                      },
                    ),
                  ),
                );
                break;
            }
          } else {
            // Fallback to old logic for backward compatibility
            switch(title.toString()) {
              case DashboardStrings.notesTitle:
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => BlocProvider(
                      create: (context) => sl<NotesCubit>(),
                      child: NotesPage(school: school, etudiant: etudiant),
                    ),
                    settings: RouteSettings(
                      arguments: {
                        'school': school,
                        'etudiant': etudiant,
                      },
                    ),
                  ),
                );
                break;
              case DashboardStrings.financesTitle:
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => BlocProvider(
                        create: (context) => sl<FinancesCubit>(),
                        child: FinancesPage(school: school, etudiant: etudiant),
                      ),
                    ),
                  );
                
                break;
              case DashboardStrings.absencesTitle:
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => BlocProvider(
                      create: (context) => sl<AbsencesRetardsCubit>(),
                      child: AbsencesRetardsPage(school: school, etudiant: etudiant),
                    ),
                    settings: RouteSettings(
                      arguments: {
                        'school': school,
                        'etudiant': etudiant,
                      },
                    ),
                  ),
                );
                break;
              case DashboardStrings.dossiersTitle:
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => BlocProvider(
                      create: (context) => sl<DossiersCubit>(),
                      child: DossiersPage(school: school, etudiant: etudiant),
                    ),
                    settings: RouteSettings(
                      arguments: {
                        'school': school,
                        'etudiant': etudiant,
                      },
                    ),
                  ),
                );
                break;
              case DashboardStrings.cahierTitle:
                 Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => BlocProvider(
                      create: (context) => sl<CourseLogCubit>(),
                      child: CahierTextePage(school: school, etudiant: etudiant),
                    ),
                    settings: RouteSettings(
                      arguments: {
                        'school': school,
                        'etudiant': etudiant,
                      },
                    ),
                  ),
                );
                break;
              case DashboardStrings.planningTitle:
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => BlocProvider(
                      create: (context) => sl<EmploiDuTempsCubit>(),
                      child: EmploiDuTempsPage(school: school, etudiant: etudiant),
                    ),
                    settings: RouteSettings(
                      arguments: {
                        'school': school,
                        'etudiant': etudiant,
                      },
                    ),
                  ),
                );
                break;
              case DashboardStrings.virtualCardTitle:
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => CarteVirtuellePage(school: school, etudiant: etudiant),
                    settings: RouteSettings(
                      arguments: {
                        'school': school,
                        'etudiant': etudiant,
                      },
                    ),
                  ),
                );
                break;
              default :
                 ScaffoldMessenger.of(context).showSnackBar(
                CustomSnackbar(message: "Fonctionnalité en cours de développement").getSnackBar()
              );
                break;
            }
          }
        },
      ),
    );
  }

  /// Handle financial control with QR code scanning
  void _handleFinancialControl(
    BuildContext context,
    EtablissementUtilisateur school,
    EnfantTuteurEntity? etudiant,
  ) async {
    try {
      // Launch QR code scanner
      await Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => MobileScanner(
            controller: MobileScannerController(
              detectionSpeed: DetectionSpeed.normal,
              formats: const [BarcodeFormat.qrCode],
            ),
            onDetect: (BarcodeCapture capture) {
              // Handle barcode detection
              final String? scannedValue = capture.barcodes.first.rawValue;
              debugPrint('MobileScanner : Scanned value: $scannedValue');
              if (scannedValue != null && scannedValue.isNotEmpty) {
                Navigator.of(context).pop(); // Close scanner

                // Navigate to ControleFinancierPage with scanned value
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => MultiBlocProvider(
                      providers: [
                        BlocProvider(
                          create: (context) => sl<FinancesCubit>(),
                        ),
                        BlocProvider(
                          create: (context) => sl<FinancialStatusCubit>(),
                        ),
                      ],
                      child: ControleFinancierPage(
                        school: school,
                        etudiant: etudiant,
                        scannedValue: scannedValue,
                      ),
                    ),
                    settings: RouteSettings(
                      arguments: {
                        'school': school,
                        'etudiant': etudiant,
                        'scannedValue': scannedValue,
                      },
                    ),
                  ),
                );
              }
            },
          ),
        ),
      );
    } catch (e) {
      // Handle scanner errors
      debugPrint('QR Scanner error: $e');

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de l\'ouverture du scanner QR: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );

        // Fallback: Navigate to page without scanned value
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => MultiBlocProvider(
              providers: [
                BlocProvider(
                  create: (context) => sl<FinancesCubit>(),
                ),
                BlocProvider(
                  create: (context) => sl<FinancialStatusCubit>(),
                ),
              ],
              child: ControleFinancierPage(
                school: school,
                etudiant: etudiant,
              ),
            ),
            settings: RouteSettings(
              arguments: {
                'school': school,
                'etudiant': etudiant,
              },
            ),
          ),
        );
      }
    }
  }
}
