import 'package:Kairos/features/educational_resources/domain/entities/resource_pedagogique_entity.dart';

/// Data model for an educational resource item
class ResourcePedagogiqueModel {
  final String typeRessource;
  final String couleurTypeRessource;
  final String dateCreation;
  final String titre;
  final String description;
  final String professeur;
  final String classe;
  final bool hasAttachment;
  final int idRessource;
  final String? contenuDetaille;
  final String? emailProfesseur;

  ResourcePedagogiqueModel({
    required this.typeRessource,
    required this.couleurTypeRessource,
    required this.dateCreation,
    required this.titre,
    required this.description,
    required this.professeur,
    required this.classe,
    required this.hasAttachment,
    required this.idRessource,
    this.contenuDetaille,
    this.emailProfesseur,
  });

  /// Factory constructor to create ResourcePedagogiqueModel from JSON
  factory ResourcePedagogiqueModel.fromJson(Map<String, dynamic> json) {
    return ResourcePedagogiqueModel(
      typeRessource: json['typeRessource'] ?? '',
      couleurTypeRessource: json['couleurTypeRessource'] ?? '#A5351F',
      dateCreation: json['dateCreation'] ?? '',
      titre: json['titre'] ?? '',
      description: json['description'] ?? '',
      professeur: json['professeur'] ?? '',
      classe: json['classe'] ?? '',
      hasAttachment: json['hasAttachment'] ?? false,
      idRessource: json['idRessource'] ?? 0,
      contenuDetaille: json['contenuDetaille'],
      emailProfesseur: json['emailProfesseur'],
    );
  }

  /// Method to convert ResourcePedagogiqueModel to JSON (optional, but good practice)
  Map<String, dynamic> toJson() {
    return {
      'typeRessource': typeRessource,
      'couleurTypeRessource': couleurTypeRessource,
      'dateCreation': dateCreation,
      'titre': titre,
      'description': description,
      'professeur': professeur,
      'classe': classe,
      'hasAttachment': hasAttachment,
      'idRessource': idRessource,
      'contenuDetaille': contenuDetaille,
      'emailProfesseur': emailProfesseur,
    };
  }

  /// Method to convert ResourcePedagogiqueModel to ResourcePedagogiqueEntity
  ResourcePedagogiqueEntity toEntity() {
    return ResourcePedagogiqueEntity(
      typeRessource: typeRessource,
      couleurTypeRessource: couleurTypeRessource,
      dateCreation: dateCreation,
      titre: titre,
      description: description,
      professeur: professeur,
      classe: classe,
      hasAttachment: hasAttachment,
      idRessource: idRessource,
      contenuDetaille: contenuDetaille,
      emailProfesseur: emailProfesseur,
    );
  }
}
