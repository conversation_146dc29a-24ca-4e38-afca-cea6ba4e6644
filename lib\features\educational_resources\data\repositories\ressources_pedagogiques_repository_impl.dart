import 'package:dartz/dartz.dart';
import 'package:Kairos/core/error/exceptions.dart';
import 'package:Kairos/core/error/failures.dart';
import 'package:Kairos/features/educational_resources/data/datasources/ressources_pedagogiques_remote_datasource.dart';
import 'package:Kairos/features/educational_resources/domain/entities/resource_pedagogique_entity.dart';
import 'package:Kairos/features/educational_resources/domain/repositories/ressources_pedagogiques_repository.dart';
import 'package:Kairos/features/student_records/domain/entities/dossier_attachment_entity.dart';

/// Implementation of RessourcesPedagogiquesRepository
class RessourcesPedagogiquesRepositoryImpl implements RessourcesPedagogiquesRepository {
  final RessourcesPedagogiquesRemoteDataSource remoteDataSource;

  RessourcesPedagogiquesRepositoryImpl({
    required this.remoteDataSource,
  });

  @override
  Future<Either<Failure, List<ResourcePedagogiqueEntity>>> getRessourcesPedagogiques({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  }) async {
    try {
      final remoteRessources = await remoteDataSource.getRessourcesPedagogiques(
        codeEtab: codeEtab,
        telephone: telephone,
        codeEtudiant: codeEtudiant,
        codeUtilisateur: codeUtilisateur,
      );
      return Right(remoteRessources.map((model) => model.toEntity()).toList());
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    }
  }

  @override
  Future<Either<Failure, List<ResourcePedagogiqueEntity>>> getRessourcesPedagogiquesFiltres({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    required String annee,
    String? codeUtilisateur,
  }) async {
    try {
      final remoteRessources = await remoteDataSource.getRessourcesPedagogiquesFiltres(
        codeEtab: codeEtab,
        telephone: telephone,
        codeEtudiant: codeEtudiant,
        annee: annee,
        codeUtilisateur: codeUtilisateur,
      );
      return Right(remoteRessources.map((model) => model.toEntity()).toList());
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    }
  }

  @override
  Future<Either<Failure, DossierAttachmentEntity>> getRessourcePedagogiqueAttachment({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    required int idRessource,
    String? codeUtilisateur,
  }) async {
    try {
      final remoteAttachment = await remoteDataSource.getRessourcePedagogiqueAttachment(
        codeEtab: codeEtab,
        telephone: telephone,
        codeEtudiant: codeEtudiant,
        idRessource: idRessource,
        codeUtilisateur: codeUtilisateur,
      );
      return Right(remoteAttachment.toEntity());
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    }
  }
}
