import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:Kairos/core/error/failures.dart';
import 'package:Kairos/core/usecases/usecase.dart';
import 'package:Kairos/features/educational_resources/domain/entities/resource_pedagogique_entity.dart';
import 'package:Kairos/features/educational_resources/domain/repositories/ressources_pedagogiques_repository.dart';

/// Use case for fetching educational resources
class GetRessourcesPedagogiquesUseCase implements UseCase<List<ResourcePedagogiqueEntity>, GetRessourcesPedagogiquesParams> {
  final RessourcesPedagogiquesRepository repository;

  GetRessourcesPedagogiquesUseCase(this.repository);

  @override
  Future<Either<Failure, List<ResourcePedagogiqueEntity>>> call(
      GetRessourcesPedagogiquesParams params) async {
    return await repository.getRessourcesPedagogiques(
      codeEtab: params.codeEtab,
      telephone: params.telephone,
      codeEtudiant: params.codeEtudiant,
      codeUtilisateur: params.codeUtilisateur,
    );
  }
}

/// Parameters for GetRessourcesPedagogiquesUseCase
class GetRessourcesPedagogiquesParams extends Equatable {
  final String codeEtab;
  final String telephone;
  final String codeEtudiant;
  final String? codeUtilisateur;

  const GetRessourcesPedagogiquesParams({
    required this.codeEtab,
    required this.telephone,
    required this.codeEtudiant,
    this.codeUtilisateur,
  });

  @override
  List<Object?> get props => [codeEtab, telephone, codeEtudiant, codeUtilisateur];
}
