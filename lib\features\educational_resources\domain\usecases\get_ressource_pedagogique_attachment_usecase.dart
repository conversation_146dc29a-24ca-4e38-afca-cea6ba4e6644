import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:Kairos/core/error/failures.dart';
import 'package:Kairos/core/usecases/usecase.dart';
import 'package:Kairos/features/student_records/domain/entities/dossier_attachment_entity.dart';
import 'package:Kairos/features/educational_resources/domain/repositories/ressources_pedagogiques_repository.dart';

/// Use case for fetching educational resource attachment
class GetRessourcePedagogiqueAttachmentUseCase implements UseCase<DossierAttachmentEntity, GetRessourcePedagogiqueAttachmentParams> {
  final RessourcesPedagogiquesRepository repository;

  GetRessourcePedagogiqueAttachmentUseCase(this.repository);

  @override
  Future<Either<Failure, DossierAttachmentEntity>> call(
      GetRessourcePedagogiqueAttachmentParams params) async {
    return await repository.getRessourcePedagogiqueAttachment(
      codeEtab: params.codeEtab,
      telephone: params.telephone,
      codeEtudiant: params.codeEtudiant,
      idRessource: params.idRessource,
      codeUtilisateur: params.codeUtilisateur,
    );
  }
}

/// Parameters for GetRessourcePedagogiqueAttachmentUseCase
class GetRessourcePedagogiqueAttachmentParams extends Equatable {
  final String codeEtab;
  final String telephone;
  final String codeEtudiant;
  final int idRessource;
  final String? codeUtilisateur;

  const GetRessourcePedagogiqueAttachmentParams({
    required this.codeEtab,
    required this.telephone,
    required this.codeEtudiant,
    required this.idRessource,
    this.codeUtilisateur,
  });

  @override
  List<Object?> get props => [codeEtab, telephone, codeEtudiant, idRessource, codeUtilisateur];
}
