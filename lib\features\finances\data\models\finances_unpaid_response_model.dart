import 'package:Kairos/features/finances/data/models/frais_etudiant_model.dart';

class FinancesUnpaidResponseModel {
  final double? totalImpaye;
  final List<FraisEtudiantModel>? fraisImPayes;

  FinancesUnpaidResponseModel({
    this.totalImpaye,
    this.fraisImPayes,
  });

  factory FinancesUnpaidResponseModel.fromJson(Map<String, dynamic> json) {
    return FinancesUnpaidResponseModel(
      totalImpaye: json['totalImpaye']?.toDouble(),
      fraisImPayes: (json['fraisImpayes'] as List<dynamic>?)
          ?.map((item) => FraisEtudiantModel.fromJson(item as Map<String, dynamic>))
          .toList(),
    );
  }
}
