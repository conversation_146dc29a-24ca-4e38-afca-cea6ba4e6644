import 'package:Kairos/core/widgets/common/hero_widget.dart';
import 'package:Kairos/core/widgets/common/snackbar_widget.dart';
import 'package:Kairos/features/authentication/data/datasources/auth_local_datasource.dart';
import 'package:Kairos/features/schools/domain/entities/etablissement_utilisateur.dart';
import 'package:Kairos/features/schools/presentation/bloc/schools_cubit.dart';
import 'package:Kairos/features/schools/presentation/bloc/schools_state.dart'; // Import SchoolsState
import 'package:Kairos/features/profile/presentation/bloc/profile_cubit.dart'; // New import
import 'package:Kairos/features/profile/presentation/bloc/profile_state.dart'; // New import
import 'package:Kairos/core/services/device_info_service.dart'; // New import
import 'package:Kairos/features/authentication/data/models/delete_account_request.dart'; // New import
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:flutter_bloc/flutter_bloc.dart'; // Import BlocConsumer
import 'package:Kairos/features/schools/presentation/pages/liste_etablissements/liste_etablissement_widgets/list_etablisssement_utilisateur.widget.dart';
import 'package:Kairos/features/schools/presentation/pages/liste_etablissements/liste_etablissement_widgets/liste_empty_alert.widget.dart';
import 'package:Kairos/core/widgets/indicators/custom_spinner.dart';
import 'package:Kairos/core/di/injection_container.dart'; // Import sl
import 'package:Kairos/core/widgets/dialogs/app_alert_dialog.dart';
import 'package:Kairos/core/theme/color_schemes.dart';

class ListeEtablissement extends StatefulWidget{
  const ListeEtablissement({super.key});

  @override
  State<ListeEtablissement> createState() => _ListeEtablissementState();
}

class _ListeEtablissementState extends State<ListeEtablissement>{
  // Inject SchoolsLocalDataSource
  final AuthLocalDataSource _authLocalDataSource = sl<AuthLocalDataSource>();
  final List<EtablissementUtilisateur> _userSchools = [];

  @override
  void initState() {
    super.initState();
    // Fetch user schools when the page initializes
    _fetchUserSchools();
  }

  Future<void> _fetchUserSchools() async {
    final String? phoneNumber = await _authLocalDataSource.getPhoneNumber();
    if (phoneNumber != null) {
      _userSchools.addAll(await context.read<SchoolsCubit>().getUserSchools(phoneNumber));
    } else {
      // Handle case where phone number is not available
      debugPrint('Phone number not available.');
      // Optionally dispatch an error state to the cubit
    }
  }


  Future<void> _deleteAccount(BuildContext buildContext) async {
    try {
      // Get device info
      final deviceInfo = DeviceInfoService.deviceInfo;
      final String? phoneNumber = await _authLocalDataSource.getPhoneNumber();

      if (phoneNumber == null) {
        ScaffoldMessenger.of(buildContext).showSnackBar(
          CustomSnackbar(message: "Numéro de téléphone non disponible.").getSnackBar(),
        );
        return;
      }

      // Create delete account request
      final request = DeleteAccountRequest(
        numeroTelephone: phoneNumber,
        marqueTelephone: deviceInfo.marqueTelephone,
        modelTelephone: deviceInfo.modelTelephone,
        imeiTelephone: deviceInfo.imeiTelephone,
        numeroSerie: deviceInfo.numeroSerie,
      );

      debugPrint('ListeEtablissement: Delete account request: ${request.toJson()}');
      // Call delete account
      context.read<ProfileCubit>().deleteAccount(request);
    } catch (e) {
      debugPrint('Error creating delete account request: $e');
      ScaffoldMessenger.of(buildContext).showSnackBar(
        CustomSnackbar(message: "Erreur lors de la préparation de la suppression du compte.").getSnackBar(),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      body: BlocConsumer<SchoolsCubit, SchoolsState>(
        listener: (context, schoolsState) {
          // Listener for SchoolsCubit states
          if (schoolsState is SchoolsError) {
            debugPrint('Schools Error: ${schoolsState.message}');
            ScaffoldMessenger.of(context).showSnackBar(
              CustomSnackbar(message: schoolsState.message).getSnackBar(),
            );
          } else if (schoolsState is SchoolsLoaded) {
            debugPrint('Schools loaded: ${schoolsState.data.length}');
          }
        },
        builder: (context, schoolsState) {
          return BlocListener<ProfileCubit, ProfileState>(
            listener: (context, profileState) async {
              // Listener for ProfileCubit states
              if (profileState is ProfileDeleted) {
                // Clear local cache after successful deletion
                await _authLocalDataSource.clearCache();
                ScaffoldMessenger.of(context).showSnackBar(
                  CustomSnackbar(
                    message: "Votre compte a été supprimé avec succès.",
                  ).getSnackBar(),
                );
                // Navigate to /accueil page after successful deletion
                Navigator.pushNamedAndRemoveUntil(context, "/accueil", (route) => false, arguments:{'goToPhoneAuth': true });
              } else if (profileState is ProfileError) {
                ScaffoldMessenger.of(context).showSnackBar(
                  CustomSnackbar(message: profileState.message).getSnackBar(),
                );
              }
            },
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                HeroWidget(),
                const SizedBox(height: 20),
                Text(schoolsState is SchoolsLoaded && schoolsState.data.isEmpty? "ACTIVATION D'UN NOUVEL ÉTABLISSEMENT": "ÉTABLISSEMENTS LIÉS AU PROFIL", textAlign: TextAlign.center, style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
                Divider(color: Theme.of(context).primaryColor, thickness: 4, height: 20, indent: 150, endIndent: 150,),
                const SizedBox(height: 2),
                if(schoolsState is SchoolsLoaded && schoolsState.data.isNotEmpty)
                  Text("Veuillez choisir un établissement pour vous y connecter", textAlign: TextAlign.center, style: TextStyle(fontSize: 12, color: Colors.grey.shade800),),
                if (schoolsState is SchoolsLoading || schoolsState is ProfileDeleting) // Handle ProfileDeleting as a loading state
                  // Display loading indicator
                  const Flexible(
                    flex: 12,
                    child: Center(
                      child: CustomSpinner(
                        size: 60.0,
                        strokeWidth: 5.0,
                      ),
                    ),
                  )
                else if (schoolsState is SchoolsLoaded)
                  // Display the list of schools
                  if (schoolsState.data.isNotEmpty) // Use schoolsState.data
                    Flexible(
                      flex: 6,
                      child: Center(
                        child: Column(
                          children: [
                            ListeEtablissementUtilisateurWidget(
                              userSchools: schoolsState.data.isEmpty? _userSchools: schoolsState.data, // Use schoolsState.data
                            ),
                          ],
                        ),
                      ),
                    )
                  else
                    // Display empty state message
                    const Flexible(
                      flex: 6,
                      child: ListeEmptyAlert(),
                    )
                else if (schoolsState is SchoolsError)
                  // Display error message (optional, can be handled by listener)
                  Flexible(
                    flex: 6,
                    child: Center(
                      child: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            SvgPicture.asset("assets/icons/icon_aucun_etablissement.svg"),
                            Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: Center(
                                child: Text(
                                  "Erreur: ${schoolsState.message.contains('Champ requis manquant') && schoolsState.message.contains('token') ? 'Votre session a expiré, veuillez vous reconnecter.' : schoolsState.message}",
                                  textAlign: TextAlign.center,
                                  ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  )
                else
                  // Initial state or other states
                  Container(), // Or a default widget
                const SizedBox(height: 20),
                if(schoolsState is! SchoolsError) FilledButton(
                  style: ButtonStyle(
                    backgroundColor: WidgetStateProperty.all(Theme.of(context).primaryColor),
                    fixedSize: WidgetStateProperty.all(const Size(300, 50))
                  ),
                  onPressed: () async {
                    debugPrint('the user clicked on `Continue` button');
                    final userSchools = await Navigator.pushNamed(context, "/activate_school");
                    debugPrint('userSchools: $userSchools');
                    if(userSchools == null || ( (userSchools as List<EtablissementUtilisateur>).isNotEmpty)){
                      await _fetchUserSchools();
                    }
                  },
                  child: Text(schoolsState is SchoolsLoaded && schoolsState.data.isEmpty? "ACTIVER UN ÉTABLISSEMENT": "CRÉER UN NOUVEL ÉTABLISSEMENT", style: const TextStyle(fontWeight: FontWeight.bold), ),
                ) else if((schoolsState.message.contains('Champ requis manquant') && schoolsState.message.contains('token')) ||
                (schoolsState.message.contains('token') && schoolsState.message.contains("expiré")))
                  
                          FilledButton(
                            style: ButtonStyle(
                              backgroundColor: WidgetStateProperty.all(Theme.of(context).primaryColor),
                              minimumSize: WidgetStateProperty.all(const Size(150, 50)),
                            ),
                            onPressed: () async {
                              await Navigator.pushNamed(
                                context,
                                "/accueil",
                                arguments: {'goToPhoneAuth': true},
                              );
                            },
                            child: const Text("SE RECONNECTER")),
                const SizedBox(height: 10),
                _buildDeleteAccountSection(),
                const SizedBox(height: 20),
                SvgPicture.asset("assets/images/logo_footer.svg"),
                const SizedBox(height: 10)
              ],
            ),
          );
        },
      ),
    );
  }

    Widget _buildDeleteAccountSection() {
    return Center(
      child: GestureDetector(
        onTap: () {
          showDialog(
            context: context,
            builder: (BuildContext context) {
              return AppAlertDialog(
                title: "SUPPRESSION DE COMPTE",
                barrierDismissible: false,
                content: RichText(
                  textAlign: TextAlign.center,
                  text: TextSpan(
                    children: [
                      const TextSpan(
                        text: "Vous serez déconnecté de votre profil !\n",
                        style: TextStyle(
                          color: Colors.black,
                          fontSize: 14,
                        ),
                      ),
                      TextSpan(
                        text: "En continuant, les informations renseignées seront supprimées de cet appareil.\nVoulez-vous continuer ?",
                        style: TextStyle(
                          color: AppColorSchemes.errorRed,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
                primaryActionText: 'CONFIFMER',
                primaryActionCallback: () => _deleteAccount(context),
              );
            },
          );
        },
        child: const Text(
          'Supprimer mon compte',
          style: TextStyle(
           
            fontSize: 13,
            fontWeight: FontWeight.w300,
            color: Color(0xFF920000),
            height: 1.21,
          ),
        ),
      ),
    );
  }
}